using Visfuture.OneTeam.Ticket.Api.Extensions;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

//builder.Services.ConfigureRedisCache(builder.Configuration);
builder.Services.ConfigureDbContext(builder.Configuration);
builder.Services.ConfigureJWT(builder.Configuration);
builder.Services.ConfigureExceptionHandler();
builder.Services.ConfigureCustomServices();

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddMapsterServices();
builder.Services.AddSwaggerGen();
builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", policyBuilder =>
    {
        policyBuilder
            //.WithOrigins("http://localhost:5173", "http://localhost:5173", "http://localhost:7250", "https://localhost:7250")
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
        //.AllowCredentials();
    });
});


WebApplication app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("CorsPolicy");

//app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();