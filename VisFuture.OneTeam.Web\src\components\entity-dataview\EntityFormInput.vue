<script lang="ts" setup>
import { Ref, computed, onMounted, ref, shallowRef, watch, nextTick } from 'vue'
import { codes } from '../../data/CurrencyCodes'
import { BaseEntity } from '../../data/entities/BaseEntity'
import { BaseKey, BadgeOp, CodeTypeOp, CurrencyOp, FileOp, Info, SelectOp, SyncOp, LinkOp } from '../../data/FieldTypes'
import { schema, SchemaKey } from '../../data/schema'
import { validators } from '../../services/utils'
import { formatTime, parseTime } from '../../services/dateTimeUtils'
import { parseDateTime } from '../../services/datetimeParser'
import { baseUrls } from '../../services/api/requests'
import { useActiveElement, watchIgnorable } from '@vueuse/core'

import { getExternMap } from '../../services/externUtils'

import CodeTypeSelect from './CodeTypeSelect.vue'
import ExternalSelect from './ExternalSelect.vue'

import { useRouter } from 'vue-router'

onMounted(async () => {
  if (
    field.type === 'link' &&
    !selectedExtern.value[props.objKey] &&
    typeof newEntity.value[props.objKey] === 'string'
  ) {
    const id = newEntity.value[props.objKey] as string
    const map = await getExternMap([id], props.type, props.objKey)
    selectedExtern.value[props.objKey] = map[id]
  }
})

const router = useRouter()

const goToLinkedEntity = () => {
  const op = options as LinkOp<BaseEntity>
  const id = newEntity.value[props.objKey]

  if (!id) return

  const routePrefix = op.routePrefix ?? `/tenant-admin/${op.entity.toLowerCase()}`

  router.push({
    path: routePrefix,
    query: { selectedId: id instanceof Date ? id.toISOString() : String(id) },
  })
}
const props = defineProps<{
  objKey: BaseKey
  type: SchemaKey
  default?: any // field must have this value, non-editable
  inline?: boolean
}>()

const emit = defineEmits<{
  (event: 'focusout'): void
}>()

const newEntity = defineModel({ required: true }) as Ref<BaseEntity>
const info = schema[props.type] as Info<BaseEntity>
const field = info.fields[props.objKey]
const options = info.options?.[props.objKey]
const required = field.required
const disabled = field.type === 'code' || (field.disabled && !(!newEntity.value.id && required))
const rule = required ? [validators.required] : []
const label = props.inline ? undefined : field.label + (field.type !== 'bool' && required ? ' *' : '')
const keyType = field.type
const locale = navigator.language
const hasDefault = !!props.default
const size = props.inline ? (keyType === 'bool' ? 'w-16' : 'w-64') : 'w-full'

const currencySymbol = () => {
  const code = newEntity.value[(options as CurrencyOp<BaseEntity>).currencyKey] as keyof typeof codes
  if (!code) return ''
  return codes[code].symbol_native
}

// focus input field if inline, exit if focus is lost
const cellRef = shallowRef()
if (props.inline) {
  onMounted(() => {
    if (props.inline) {
      ;(cellRef.value as HTMLElement).focus()
    }
  })
  const activeElement = useActiveElement()
  watch(activeElement, (el) => {
    if (el?.nodeName === 'BODY') {
      emit('focusout')
    }
  })
}

const selectedExtern = ref<Record<string, BaseEntity | undefined>>({})
if (field.type === 'datetime') {
  // fix time resetting when date changes
  const { ignoreUpdates } = watchIgnorable(
    computed(() => newEntity.value[props.objKey]?.toString()),
    (newDateString, oldDateString) => {
      const x = new Date(newDateString!)
      const oldDate = new Date(oldDateString!)
      const newDate = new Date(oldDate)
      if (x.toDateString() != oldDate.toDateString()) {
        newDate.setFullYear(x.getFullYear(), x.getMonth(), x.getDate())
        ignoreUpdates(() => {
          ;(newEntity.value[props.objKey] as Date) = newDate
        })
      }
      if ((options as any)?.sync) {
        ;(options as SyncOp<BaseEntity>).sync(newEntity, selectedExtern)
      }
    },
  )
} else {
  // call sync function when input field changes
  if ((options as any)?.sync) {
    watch(
      () => newEntity.value[props.objKey],
      () => (options as SyncOp<BaseEntity>).sync(newEntity, selectedExtern),
    )
  }
}

// temporary image uploader
const imageBaseUrl = baseUrls.BaseBiz
const basic = shallowRef()
const onImageUpload = async (event: Event, key: string) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  const formData = new FormData()
  formData.append('file', file)

  try {
    const response = await fetch(`${baseUrls}/api/File/UploadImage`, {
      method: 'POST',
      body: formData,
    })

    if (response.ok) {
      const { imageId } = await response.json()
      ;(newEntity.value as any)[key] = imageId
    } else {
      console.error('Image upload failed.')
    }
  } catch (error) {
    console.error('Upload error:', error)
  }
}

const inlineEnter = () => {
  if (props.inline || keyType === 'datetime') {
    return
  }
  emit('focusout')
}

watch(
  () => newEntity.value[props.objKey],
  async (newId) => {
    if (!newId || !['link', 'external', 'filtered'].includes(field.type)) return

    const id = typeof newId === 'string' ? newId : newId instanceof Date ? newId.toISOString() : String(newId)

    const map = await getExternMap([id], props.type, props.objKey)
    selectedExtern.value[props.objKey] = undefined
    await nextTick()
    selectedExtern.value[props.objKey] = map[id]
  },
  { immediate: true },
)
// const computeLinkUrl = () => {
//   const op = options as LinkOp<BaseEntity>
//   const id = newEntity.value[props.objKey]
//   const prefix = op.routePrefix ?? `/${op.entity}`
//   return `${prefix}/${id}`
// }

const computeLinkLabel = computed(() => {
  const ext = selectedExtern.value[props.objKey]
  const labelField = (options as any)?.labelField ?? 'name'
  const linkedId = newEntity.value[props.objKey]

  const label = ext?.[labelField as keyof BaseEntity] ?? linkedId ?? 'View'
  return label
})
</script>
<template>
  <div
    ref="cellRef"
    class="flex min-w-0"
    :class="size"
    tabindex="0"
    @keydown.enter.exact="inlineEnter"
    @keydown.shift.enter.exact.prevent="emit('focusout')"
    @keydown.esc="emit('focusout')"
  >
    <VaInput
      v-if="keyType === 'smalltext'"
      v-model="(newEntity as any)[objKey]"
      :max-length="50"
      :placeholder="(objKey as string) === 'code' ? 'Auto-generated' : `Enter ${field.label}...`"
      :label="label"
      :rules="[...rule, validators.maxLength(50)]"
      :name="objKey"
      :disabled="disabled"
    />
    <VaInput
      v-else-if="keyType === 'bigtext'"
      v-model="(newEntity as any)[objKey]"
      :placeholder="`Enter ${field.label}...`"
      :label="label"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    />
    <VaSelect
      v-else-if="keyType === 'select'"
      v-model="newEntity[objKey]"
      :placeholder="`Select ${field.label}...`"
      :label="label"
      :options="(options as SelectOp).options"
      :rules="rule"
      :clearable="!required"
      searchable
      :disabled="disabled"
    />
    <ExternalSelect
      v-else-if="keyType === 'code'"
      v-model="newEntity"
      placeholder="Select Entity..."
      :from-type="type"
      :from-key="objKey"
      :option="options"
      :required="required"
      :is-code="true"
      :disabled="hasDefault || disabled"
      :inline="inline"
    />
    <ExternalSelect
      v-else-if="['external', 'filtered', 'link'].includes(keyType)"
      v-model="newEntity"
      placeholder="Select Entity..."
      :from-type="type"
      :from-key="objKey"
      :option="options"
      :required="required"
      :disabled="hasDefault || disabled"
      :inline="inline"
    />
    <div v-if="objKey === ('superiorId' as BaseKey) && selectedExtern[objKey]?.id" class="mt-2 flex items-center">
      <VaButton preset="secondary" size="small" icon="link" @click="goToLinkedEntity">
        <span>View {{ computeLinkLabel }}</span>
      </VaButton>
    </div>
    <CodeTypeSelect
      v-else-if="keyType === 'codetype'"
      v-model="newEntity"
      v-model:selected="selectedExtern"
      :from-type="type"
      :from-key="objKey"
      :type-code="(options as CodeTypeOp).typeCode"
      :required="required"
      :inline="inline"
      :disabled="disabled"
    />
    <VaSelect
      v-else-if="keyType === 'badge'"
      v-model="newEntity[objKey]"
      :label="label"
      :options="Object.keys((options as BadgeOp).badge)"
      :rules="rule"
      searchable
      :disabled="disabled"
    >
      <template #content="{ value }">
        <VaBadge
          v-if="value"
          class="m-0.5"
          square
          :color="(options as BadgeOp).badge[value]"
          :text="value.toUpperCase()"
        />
      </template>
    </VaSelect>
    <div v-else-if="keyType === 'bool'" class="flex-1 w-full ml-1 mt-2 mb-2 flex">
      <VaCheckbox
        v-model="(newEntity as any)[objKey]"
        :label="label"
        class="w-full min-w-0 self-end"
        :name="objKey"
        :disabled="disabled"
      />
    </div>
    <VaInput
      v-else-if="keyType === 'email'"
      v-model="(newEntity as any)[objKey]"
      type="email"
      inputmode="email"
      placeholder="<EMAIL>"
      :label="label"
      :rules="[...rule, validators.email]"
      :name="objKey"
      :disabled="disabled"
    />
    <VaInput
      v-else-if="keyType === 'tel'"
      v-model="(newEntity as any)[objKey]"
      placeholder="+1**************"
      :label="label"
      type="tel"
      inputmode="tel"
      :rules="[...rule, validators.tel]"
      :name="objKey"
      :disabled="disabled"
    />
    <VaInput
      v-else-if="keyType === 'place'"
      v-model="(newEntity as any)[objKey]"
      :placeholder="`Enter ${field.label}...`"
      :label="label"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    />
    <VaInput
      v-else-if="keyType === 'url'"
      v-model="(newEntity as any)[objKey]"
      placeholder="https://www.example.com"
      type="url"
      inputmode="url"
      :label="label"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    />
    <VaInput
      v-else-if="keyType === 'number'"
      v-model="(newEntity as any)[objKey]"
      placeholder="0"
      type="number"
      inputmode="numeric"
      :label="label"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    />
    <VaInput
      v-else-if="keyType === 'percent'"
      v-model="(newEntity as any)[objKey]"
      placeholder="100"
      type="number"
      inputmode="decimal"
      :label="label"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    >
      <template #appendInner> % </template>
    </VaInput>
    <VaInput
      v-else-if="keyType === 'currency'"
      v-model="(newEntity as any)[objKey]"
      placeholder="0.00"
      type="number"
      inputmode="decimal"
      :label="label"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    >
      <template #prependInner>
        {{ currencySymbol() }}
      </template>
    </VaInput>
    <VaInput
      v-else-if="keyType === 'time'"
      v-model="
        computed({
          get: () => formatTime(newEntity[objKey] as any),
          set: (val?: string) => {
            ;(newEntity[objKey] as any) = parseTime(val)
          },
        }).value
      "
      placeholder="Enter Time..."
      :label="label"
      strict-bind-input-value
      :clearable="!required"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    />
    <VaDateInput
      v-else-if="keyType === 'date'"
      v-model="newEntity[objKey]"
      placeholder="Enter Date..."
      :label="label"
      :format="(t) => (t as Date).toLocaleDateString(locale)"
      :rules="rule"
      manual-input
      :clearable="!required"
      :name="objKey"
      :disabled="disabled"
    />
    <div v-else-if="keyType === 'datetime'" class="flex-1 gap-4 flex-row w-full sm:w-1/2">
      <VaDateInput
        v-model="newEntity[objKey]"
        :label="label"
        class="min-w-0 w-3/5"
        :format-date="(t) => (t as Date).toLocaleDateString(locale)"
        :rules="rule"
        manual-input
        :disabled="disabled"
      />
      <VaTimeInput
        v-model="(newEntity as any)[objKey]"
        :label="inline ? undefined : ' '"
        icon=""
        class="min-w-0 w-2/5"
        :parse="parseDateTime(newEntity[objKey] as Date)"
        ampm
        manual-input
        :rules="rule"
        :disabled="disabled"
      />
    </div>
    <VaFileUpload
      v-else-if="keyType === 'file' && options && (options as FileOp).fileKey"
      v-model="(newEntity as any)[(options as FileOp).fileKey]"
      class="w-full"
      :label="label"
      :name="objKey"
      undo
      :disabled="disabled"
    />
    <VaTextarea
      v-else-if="keyType === 'textarea'"
      v-model="(newEntity as any)[objKey]"
      :label="label"
      class="w-full"
      :rules="rule"
      :name="objKey"
      :disabled="disabled"
    />
    <div v-else-if="objKey.toString() === 'imageId'" class="w-full min-w-0 flex-1">
      <label class="text-sm font-semibold mb-1 block">{{ label }}</label>
      <VaFileUpload v-model="basic" file-types="jpg,png" class="block" @change="onImageUpload($event, objKey)">
        <div class="custom-upload-file-area">
          <p class="mb-1">Click or drag & drop an image to upload</p>
          <img
            v-if="(newEntity as any)[objKey]"
            :src="`${imageBaseUrl.replace(/\/$/, '')}/api/File/${(newEntity as any)[objKey]}`"
            width="100"
            height="100"
            alt="Uploaded Image"
            class="rounded border object-cover"
          />
          <span v-else class="text-xs text-gray-500">No image selected</span>
        </div>
      </VaFileUpload>
    </div>
  </div>
</template>
